import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ArrowLef<PERSON>, ArrowRight, Check, User, MapPin, Eye } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ThemeToggle } from '@/components/ThemeToggle';
import BasicInfoStep from '@/components/BasicInfoStep';
import AddressStep from '@/components/AddressStep';
import ReviewStep from '@/components/ReviewStep';
import { useToast } from '@/hooks/use-toast';
import { useUsers } from '@/contexts/UserContext';

export interface UserFormData {
  name: string;
  email: string;
  street: string;
  city: string;
  zipcode: string;
}

const INITIAL_FORM_DATA: UserFormData = {
  name: '',
  email: '',
  street: '',
  city: '',
  zipcode: '',
};

const AddUser = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<UserFormData>(INITIAL_FORM_DATA);
  const [errors, setErrors] = useState<Partial<UserFormData>>({});
  const { toast } = useToast();
  const { addUser } = useUsers();
  const navigate = useNavigate();

  // Load form data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem('userFormData');
    const savedStep = localStorage.getItem('userFormStep');
    
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
    
    if (savedStep) {
      setCurrentStep(parseInt(savedStep));
    }
  }, []);

  // Save form data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('userFormData', JSON.stringify(formData));
    localStorage.setItem('userFormStep', currentStep.toString());
  }, [formData, currentStep]);

  const steps = [
    { number: 1, title: 'Basic Info', icon: User, description: 'Name and email' },
    { number: 2, title: 'Address', icon: MapPin, description: 'Location details' },
    { number: 3, title: 'Review', icon: Eye, description: 'Confirm details' },
  ];

  const validateStep = (step: number): boolean => {
    const newErrors: Partial<UserFormData> = {};

    if (step === 1) {
      if (!formData.name.trim()) newErrors.name = 'Name is required';
      if (!formData.email.trim()) {
        newErrors.email = 'Email is required';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        newErrors.email = 'Please enter a valid email address';
      }
    }

    if (step === 2) {
      if (!formData.street.trim()) newErrors.street = 'Street is required';
      if (!formData.city.trim()) newErrors.city = 'City is required';
      if (!formData.zipcode.trim()) newErrors.zipcode = 'Zip code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 3));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = () => {
    if (validateStep(1) && validateStep(2)) {
      const newUser = {
        name: formData.name,
        email: formData.email,
        phone: '(*************', // Default phone for demo
        address: {
          street: formData.street,
          city: formData.city,
          zipcode: formData.zipcode,
        },
      };

      addUser(newUser);

      toast({
        title: "✨ User Added Successfully!",
        description: `${formData.name} has been added to the system with awesome animations!`,
      });

      // Clear localStorage
      localStorage.removeItem('userFormData');
      localStorage.removeItem('userFormStep');

      // Reset form
      setFormData(INITIAL_FORM_DATA);
      setCurrentStep(1);
      setErrors({});

      // Navigate back to dashboard with a slight delay to show the toast
      setTimeout(() => navigate('/dashboard'), 1500);
    }
  };

  const updateFormData = (field: keyof UserFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <BasicInfoStep
            formData={formData}
            errors={errors}
            updateFormData={updateFormData}
          />
        );
      case 2:
        return (
          <AddressStep
            formData={formData}
            errors={errors}
            updateFormData={updateFormData}
          />
        );
      case 3:
        return <ReviewStep formData={formData} />;
      default:
        return null;
    }
  };

  const progress = (currentStep / 3) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <motion.div 
        className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <Link to="/dashboard">
                <Button variant="ghost" className="mr-4">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Add New User</h1>
                <p className="text-gray-600 dark:text-gray-300">Complete the form to add a new user</p>
              </div>
            </div>
            <ThemeToggle />
          </div>
        </div>
      </motion.div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Steps */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Card className="mb-8 bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
            <CardHeader>
              <div className="flex items-center justify-between mb-4">
                <motion.div
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  <CardTitle>Step {currentStep} of 3</CardTitle>
                </motion.div>
                <motion.span 
                  className="text-sm text-gray-500"
                  initial={{ x: 20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.3 }}
                >
                  {Math.round(progress)}% Complete
                </motion.span>
              </div>
              <motion.div
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="origin-left"
              >
                <Progress value={progress} className="h-3" />
              </motion.div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                {steps.map((step, index) => {
                  const Icon = step.icon;
                  const isActive = currentStep === step.number;
                  const isCompleted = currentStep > step.number;
                  
                  return (
                    <div key={step.number} className="flex items-center">
                      <div className="flex flex-col items-center">
                        <motion.div
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.95 }}
                          animate={{
                            scale: isActive ? 1.05 : 1,
                            backgroundColor: isCompleted 
                              ? 'rgb(34, 197, 94)' 
                              : isActive 
                              ? 'rgb(59, 130, 246)' 
                              : 'rgb(243, 244, 246)',
                          }}
                          transition={{ duration: 0.3 }}
                          className={`
                            w-14 h-14 rounded-full flex items-center justify-center border-2 transition-all shadow-lg
                            ${isCompleted 
                              ? 'border-green-500 text-white' 
                              : isActive 
                              ? 'border-blue-500 text-white' 
                              : 'border-gray-300 dark:border-gray-600 text-gray-400'
                            }
                          `}
                        >
                          <motion.div
                            animate={{ rotate: isCompleted ? 360 : 0 }}
                            transition={{ duration: 0.5 }}
                          >
                            {isCompleted ? (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ duration: 0.3 }}
                              >
                                <Check className="h-6 w-6" />
                              </motion.div>
                            ) : (
                              <Icon className="h-6 w-6" />
                            )}
                          </motion.div>
                        </motion.div>
                        <motion.div 
                          className="mt-3 text-center"
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 + 0.5 }}
                        >
                          <p className={`text-sm font-medium ${isActive ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300'}`}>
                            {step.title}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">{step.description}</p>
                        </motion.div>
                      </div>
                      {index < steps.length - 1 && (
                        <motion.div 
                          className={`
                            flex-1 h-1 mx-6 rounded-full transition-all duration-500
                            ${isCompleted ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-600'}
                          `}
                          initial={{ scaleX: 0 }}
                          animate={{ scaleX: 1 }}
                          transition={{ delay: 0.6 + index * 0.1 }}
                        />
                      )}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Form Content */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
        >
          <Card className="mb-8 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 shadow-xl">
            <CardHeader>
              <motion.div
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                <CardTitle className="flex items-center text-xl">
                  {React.createElement(steps[currentStep - 1].icon, { className: "h-6 w-6 mr-3 text-blue-500" })}
                  {steps[currentStep - 1].title}
                </CardTitle>
              </motion.div>
            </CardHeader>
            <CardContent>
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 30, rotateY: 10 }}
                  animate={{ opacity: 1, x: 0, rotateY: 0 }}
                  exit={{ opacity: 0, x: -30, rotateY: -10 }}
                  transition={{ duration: 0.4, type: "spring" }}
                >
                  {renderStepContent()}
                </motion.div>
              </AnimatePresence>
            </CardContent>
          </Card>
        </motion.div>

        {/* Navigation Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.6 }}
          className="flex justify-between"
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className="px-8 py-3 shadow-lg"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
          </motion.div>

          {currentStep < 3 ? (
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                onClick={handleNext}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 px-8 py-3 shadow-lg"
              >
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </motion.div>
          ) : (
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              animate={{ 
                boxShadow: ["0 0 0 0 rgba(34, 197, 94, 0.7)", "0 0 0 10px rgba(34, 197, 94, 0)", "0 0 0 0 rgba(34, 197, 94, 0)"]
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <Button
                onClick={handleSubmit}
                className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 px-8 py-3 shadow-lg"
              >
                <Check className="h-4 w-4 mr-2" />
                Add User ✨
              </Button>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default AddUser;
