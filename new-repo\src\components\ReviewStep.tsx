
import React from 'react';
import { Check, User, Mail, MapPin, Home, Hash } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { UserFormData } from '@/pages/AddUser';

interface ReviewStepProps {
  formData: UserFormData;
}

const ReviewStep = ({ formData }: ReviewStepProps) => {
  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <Check className="h-8 w-8 text-white" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900">Review Information</h2>
        <p className="text-gray-600">Please review the user details before submitting</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information Card */}
        <Card className="border-2 border-blue-100">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100">
            <CardTitle className="flex items-center text-blue-700">
              <User className="h-5 w-5 mr-2" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-4">
            <div className="flex items-center">
              <User className="h-4 w-4 text-gray-400 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Full Name</p>
                <p className="font-medium text-gray-900">{formData.name}</p>
              </div>
            </div>
            <div className="flex items-center">
              <Mail className="h-4 w-4 text-gray-400 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Email</p>
                <p className="font-medium text-gray-900">{formData.email}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Address Information Card */}
        <Card className="border-2 border-green-100">
          <CardHeader className="bg-gradient-to-r from-green-50 to-green-100">
            <CardTitle className="flex items-center text-green-700">
              <MapPin className="h-5 w-5 mr-2" />
              Address Information
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-4">
            <div className="flex items-center">
              <Home className="h-4 w-4 text-gray-400 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Street Address</p>
                <p className="font-medium text-gray-900">{formData.street}</p>
              </div>
            </div>
            <div className="flex items-center">
              <MapPin className="h-4 w-4 text-gray-400 mr-3" />
              <div>
                <p className="text-sm text-gray-500">City</p>
                <p className="font-medium text-gray-900">{formData.city}</p>
              </div>
            </div>
            <div className="flex items-center">
              <Hash className="h-4 w-4 text-gray-400 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Zip Code</p>
                <p className="font-medium text-gray-900">{formData.zipcode}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Summary Card */}
      <Card className="border-2 border-purple-100 bg-gradient-to-r from-purple-50 to-pink-50">
        <CardContent className="p-6">
          <div className="flex items-center mb-4">
            <Check className="h-5 w-5 text-purple-600 mr-2" />
            <h3 className="text-lg font-semibold text-purple-900">Ready to Submit</h3>
          </div>
          <p className="text-purple-700 mb-4">
            You're about to add <strong>{formData.name}</strong> to the user management system.
            Their information will be saved and they'll be notified at <strong>{formData.email}</strong>.
          </p>
          <div className="bg-white rounded-lg p-4 border border-purple-200">
            <p className="text-sm text-gray-600">
              <strong>Full Address:</strong> {formData.street}, {formData.city} {formData.zipcode}
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-yellow-700">
              <strong>Please review carefully:</strong> Once submitted, this user will be added to the system.
              You can always edit their information later if needed.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReviewStep;
