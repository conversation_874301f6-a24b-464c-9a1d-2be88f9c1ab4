
import React from 'react';
import { MapPin, Home, Hash } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { UserFormData } from '@/pages/AddUser';

interface AddressStepProps {
  formData: UserFormData;
  errors: Partial<UserFormData>;
  updateFormData: (field: keyof UserFormData, value: string) => void;
}

const AddressStep = ({ formData, errors, updateFormData }: AddressStepProps) => {
  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <MapPin className="h-8 w-8 text-white" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900">Address Information</h2>
        <p className="text-gray-600">Please provide the user's address details</p>
      </div>

      <div className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="street" className="text-sm font-medium text-gray-700">
            Street Address *
          </Label>
          <div className="relative">
            <Home className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              id="street"
              type="text"
              placeholder="Enter street address"
              value={formData.street}
              onChange={(e) => updateFormData('street', e.target.value)}
              className={`pl-10 ${errors.street ? 'border-red-500 focus:border-red-500' : ''}`}
            />
          </div>
          {errors.street && (
            <p className="text-sm text-red-600 mt-1">{errors.street}</p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="city" className="text-sm font-medium text-gray-700">
              City *
            </Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                id="city"
                type="text"
                placeholder="Enter city"
                value={formData.city}
                onChange={(e) => updateFormData('city', e.target.value)}
                className={`pl-10 ${errors.city ? 'border-red-500 focus:border-red-500' : ''}`}
              />
            </div>
            {errors.city && (
              <p className="text-sm text-red-600 mt-1">{errors.city}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="zipcode" className="text-sm font-medium text-gray-700">
              Zip Code *
            </Label>
            <div className="relative">
              <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                id="zipcode"
                type="text"
                placeholder="Enter zip code"
                value={formData.zipcode}
                onChange={(e) => updateFormData('zipcode', e.target.value)}
                className={`pl-10 ${errors.zipcode ? 'border-red-500 focus:border-red-500' : ''}`}
              />
            </div>
            {errors.zipcode && (
              <p className="text-sm text-red-600 mt-1">{errors.zipcode}</p>
            )}
          </div>
        </div>
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-green-700">
              <strong>Note:</strong> This address will be used for user location and shipping purposes.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddressStep;
