
import React from 'react';
import { Mail, Phone, MapPin, Trash2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { useUsers } from '@/contexts/UserContext';
import { useToast } from '@/hooks/use-toast';

interface User {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: {
    city: string;
    street: string;
    zipcode: string;
  };
}

interface UserCardProps {
  user: User;
}

const UserCard = ({ user }: UserCardProps) => {
  const { deleteUser } = useUsers();
  const { toast } = useToast();

  const handleDelete = () => {
    deleteUser(user.id);
    toast({
      title: "User Deleted",
      description: `${user.name} has been removed from the system.`,
      variant: "destructive",
    });
  };

  return (
    <motion.div
      whileHover={{ 
        scale: 1.03,
        rotateY: 2,
        rotateX: 2,
      }}
      whileTap={{ scale: 0.98 }}
      transition={{ 
        duration: 0.3,
        type: "spring",
        stiffness: 300,
        damping: 20
      }}
      layout
      initial={{ opacity: 0, y: 20, rotateX: -10 }}
      animate={{ opacity: 1, y: 0, rotateX: 0 }}
      exit={{ 
        opacity: 0, 
        y: -20, 
        scale: 0.9,
        rotateX: 10,
        transition: { duration: 0.2 }
      }}
    >
      <Card className="group hover:shadow-2xl transition-all duration-500 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 border-gray-200 dark:border-gray-700 relative overflow-hidden">
        {/* Animated background gradient */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          initial={false}
        />
        
        <CardContent className="p-6 relative z-10">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <motion.h3 
                className="text-lg font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"
                whileHover={{ x: 5 }}
                transition={{ duration: 0.2 }}
              >
                {user.name}
              </motion.h3>
              <motion.div 
                className="w-12 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full mt-2"
                initial={{ width: 0 }}
                animate={{ width: 48 }}
                transition={{ delay: 0.2, duration: 0.5 }}
              />
            </div>
            <div className="flex items-center space-x-2">
              <motion.div 
                className="w-12 h-12 bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 rounded-full flex items-center justify-center"
                whileHover={{ rotate: 10, scale: 1.1 }}
                transition={{ duration: 0.2 }}
              >
                <span className="text-blue-600 dark:text-blue-400 font-semibold text-lg">
                  {user.name.charAt(0).toUpperCase()}
                </span>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleDelete}
                  className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 opacity-0 group-hover:opacity-100 transition-all duration-300"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </motion.div>
            </div>
          </div>

          <motion.div 
            className="space-y-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, staggerChildren: 0.1 }}
          >
            <motion.div 
              className="flex items-center text-gray-600 dark:text-gray-300"
              whileHover={{ x: 5 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                whileHover={{ rotate: 10, scale: 1.2 }}
                transition={{ duration: 0.2 }}
              >
                <Mail className="h-4 w-4 mr-3 text-blue-500" />
              </motion.div>
              <span className="text-sm truncate">{user.email}</span>
            </motion.div>
            
            <motion.div 
              className="flex items-center text-gray-600 dark:text-gray-300"
              whileHover={{ x: 5 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                whileHover={{ rotate: 10, scale: 1.2 }}
                transition={{ duration: 0.2 }}
              >
                <Phone className="h-4 w-4 mr-3 text-green-500" />
              </motion.div>
              <span className="text-sm">{user.phone}</span>
            </motion.div>
            
            <motion.div 
              className="flex items-center text-gray-600 dark:text-gray-300"
              whileHover={{ x: 5 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                whileHover={{ rotate: 10, scale: 1.2 }}
                transition={{ duration: 0.2 }}
              >
                <MapPin className="h-4 w-4 mr-3 text-red-500" />
              </motion.div>
              <span className="text-sm truncate">{user.address.city}</span>
            </motion.div>
          </motion.div>

          <motion.div 
            className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {user.address.street}, {user.address.zipcode}
            </p>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default UserCard;
