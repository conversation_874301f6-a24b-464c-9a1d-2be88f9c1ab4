import React, { createContext, useContext, useState, useEffect } from 'react';

interface User {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: {
    city: string;
    street: string;
    zipcode: string;
  };
}

interface UserContextType {
  users: User[];
  addUser: (user: Omit<User, 'id'>) => void;
  deleteUser: (id: number) => void;
  setApiUsers: (users: User[]) => void;
  loading: boolean;
  setLoading: (loading: boolean) => void;
  error: string | null;
  setError: (error: string | null) => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children }: { children: React.ReactNode }) {
  const [apiUsers, setApiUsersState] = useState<User[]>([]);
  const [localUsers, setLocalUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load local users from localStorage on mount
  useEffect(() => {
    const savedUsers = localStorage.getItem('localUsers');
    if (savedUsers) {
      setLocalUsers(JSON.parse(savedUsers));
    }
  }, []);

  // Save local users to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('localUsers', JSON.stringify(localUsers));
  }, [localUsers]);

  const addUser = (userData: Omit<User, 'id'>) => {
    const newUser: User = {
      ...userData,
      id: Date.now(), // Simple ID generation
    };
    setLocalUsers(prev => [...prev, newUser]);
  };

  const deleteUser = (id: number) => {
    setLocalUsers(prev => prev.filter(user => user.id !== id));
  };

  const setApiUsers = (users: User[]) => {
    setApiUsersState(users);
  };

  // Combine API users and local users
  const allUsers = [...apiUsers, ...localUsers];

  return (
    <UserContext.Provider
      value={{
        users: allUsers,
        addUser,
        deleteUser,
        setApiUsers,
        loading,
        setLoading,
        error,
        setError,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

export function useUsers() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUsers must be used within a UserProvider');
  }
  return context;
}
